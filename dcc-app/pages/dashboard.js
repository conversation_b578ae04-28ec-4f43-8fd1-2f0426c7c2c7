import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import { supabase } from "@utils/supabaseClient";
import { Oval } from "svg-loaders-react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@core/components/ui/sidebar";
import { Separator } from "@core/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@core/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@core/components/ui/accordion";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselApi,
} from "@core/components/ui/carousel";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@core/components/ui/table";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@core/components/ui/tabs";

import { Button } from "@core/components/ui/button";
import { Badge } from "@core/components/ui/badge";

import skillsChart from "/public/skills-chart.png";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@core/components/ui/breadcrumb";
import { AppSidebar } from "@core/components/app-sidebar";

import TechSkillSummary from "@core/Dashboard/TechSkillSummary";

export default function Dashboard({
  behavioural_skills,
  technical_skills,
  tech_data_user,
}) {
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [userData, setUserData] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [userBLevel, setUserBLevel] = useState(null);
  const [userBLevelDisplay, setUserBLevelDisplay] = useState(null);
  const [userTLevel, setUserTLevel] = useState(null);
  const [userTLevelDisplay, setUserTLevelDisplay] = useState(null);
  const [userRoleDesc, setUserRoleDesc] = useState(null);
  const [filteredTechSkills, setFilteredTechSkills] = useState(null);
  const [showBSkills, setShowBSkills] = useState(false);
  const [showTSkills, setShowTSkills] = useState(true);
  const [showTabView, setShowTabView] = useState(false);
  const [carouselApi, setCarouselApi] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [totalItemsThink, setTotalItemsThink] = useState(0);
  const [totalItemsExecute, setTotalItemsExecute] = useState(0);
  const [totalItemsGrow, setTotalItemsGrow] = useState(0);

  const router = useRouter();

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if no session
        router.push("/");
      }
    });

    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (!session) {
        // Redirect to index page if session is lost
        router.push("/");
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  // Fetch user profile when session is available
  useEffect(() => {
    if (session) {
      getUserProfile();
    }
  }, [session]);

  useEffect(() => {
    if (!carouselApi) return;

    const updateCarouselState = () => {
      setCurrentIndex(carouselApi.selectedScrollSnap());
      setTotalItems(carouselApi.scrollSnapList().length);
    };

    updateCarouselState();

    carouselApi.on("select", updateCarouselState);

    return () => {
      carouselApi.off("select", updateCarouselState); // Clean up on unmount
    };
  }, [carouselApi]);

  const scrollToIndex = (index) => {
    carouselApi?.scrollTo(index);
  };

  const handleShowSkills = (value) => {
    if (value == "Behavioural") {
      setShowTSkills(false);
      setShowBSkills(true);
      setShowTabView(true);
    }

    if (value == "Technical") {
      setShowBSkills(false);
      setShowTSkills(true);
      setShowTabView(false);
    }
  };

  /* --- DEBUG --- */

  // console.log(userData);

  console.log("filteredTechSkills");
  console.log(filteredTechSkills);

  console.log("userRole");
  console.log(userRole);

  // console.log("tech_data_user");
  // console.log(tech_data_user);

  // console.log("filteredTechData");
  // console.log(filteredTechData);

  // console.log("behavioural_skills");
  // console.log(behavioural_skills);

  // console.log("technical_skills");
  // console.log(technical_skills);

  // filter the array based on user role

  /* --- DEBUG --- */

  async function getUserProfile() {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserData(data);

        // const filteredTech = tech_data_user.filter(
        //   (el) =>
        //     el.role_mapping.filter((n) => n.role_id === data.role).length > 0
        // );

        // const filteredTech2 = tech_data_user.filter(
        //   (el) =>
        //     el.role_mapping.filter((n) => n.role_id === data.role).length > 0
        // );

        // console.log("filteredTech2");
        // console.log(filteredTech2);

        // better perfomance / dont need to filter all
        const filteredTech = tech_data_user.filter(({ role_mapping }) =>
          role_mapping.some((n) => n.role_id === data.role)
        );

        setFilteredTechSkills(filteredTech);
        getUserRole(data.role);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // get user's role
  async function getUserRole(role_id) {
    try {
      setLoading(true);

      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error("No user found");
      }

      let { data, error, status } = await supabase
        .from("roles")
        .select(
          "id, team, role_name, behavioural_skill_level, role_description"
        )
        .eq("id", role_id)
        .single();

      if (error && status !== 406) {
        throw error;
      }

      if (data) {
        setUserRole(data);
        getUserBLevel(data.behavioural_skill_level);
        setUserRoleDesc(data.role_description);
      }
    } catch (error) {
      console.log("Error fetching user profile:", error.message);
      // If there's an error fetching profile, we can still show the dashboard
      // but userData will remain null
    } finally {
      setLoading(false);
    }
  }

  // set level for b skills
  const getUserBLevel = (level) => {
    switch (level) {
      case 1:
        setUserBLevel("Operational Contributor");
        setUserBLevelDisplay("level_1_description");
        break;
      case 2:
        setUserBLevel("Advanced Contributor");
        setUserBLevelDisplay("level_2_description");
        break;
      case 3:
        setUserBLevel("Team Leader");
        setUserBLevelDisplay("level_3_description");
        break;
      case 4:
        setUserBLevel("Leader of Leaders");
        setUserBLevelDisplay("level_4_description");
        break;
      case 5:
        setUserBLevel("Organisational Leader");
        setUserBLevelDisplay("level_5_description");
        break;
    }
  };

  // set level for b skills
  const getUserTLevel = (level) => {
    switch (level) {
      case 1:
        // setUserBLevel("Knowledgablee");
        return "level_1_description";
        break;
      case 2:
        // setUserBLevel("Supported Practitioner");
        return "level_2_description";
        break;
      case 3:
        // setUserBLevel("Independent Practitioner");
        return "level_3_description";
        break;
      case 4:
        // setUserBLevel("Expert");
        return "level_4_description";
        break;
    }
  };

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="grid place-items-center">
          <div className="mt-10 mb-10 flex flex-row space-x-2">
            <Oval stroke="#0c39ac" />
          </div>
        </div>
      </div>
    );
  }

  // Don't render dashboard if no session (will redirect)
  if (!session) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar
        userData={userData}
        behavioural_skills={behavioural_skills}
        technical_skills={technical_skills}
        // handleShowSkills={handleShowSkills}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-3">
            <SidebarTrigger />
            <Separator orientation="vertical" className="mr-2 h-4" />
            {showBSkills && (
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="#">Skills by Team</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbLink>Behavioural</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>
                      Cyber Security Operations Team
                    </BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            )}

            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="#">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbLink>My Skills Snapshot</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        {/* <main>{children}</main> */}

        <Tabs defaultValue="snapshot" className="relative mr-auto w-full mt-3">
          <TabsList className="w-full justify-start rounded-none border-b bg-transparent p-0">
            <TabsTrigger
              value="snapshot"
              className="relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none "
            >
              Snapshot
            </TabsTrigger>
            <TabsTrigger
              value="behavioural"
              className="relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none "
            >
              Behavioural Skills
            </TabsTrigger>
            <TabsTrigger
              value="technical"
              className="relative rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold text-muted-foreground shadow-none transition-none focus-visible:ring-0 data-[state=active]:border-b-dccpink data-[state=active]:text-foreground data-[state=active]:shadow-none "
            >
              Technical Skills
            </TabsTrigger>
          </TabsList>
          <TabsContent value="snapshot">
            {/* START - Snapshot component */}
            <div>
              <div className="grid grid-cols-2 gap-4">
                <div className="mt-1 ml-3 col-span-1">
                  <Card className="rounded-lg max-w-2xl pb-1 pt-4">
                    <CardHeader>
                      <CardTitle className="text-lg text-gray-700">
                        Behavioural Skills
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Image
                        className={"pb-0 ml-0"}
                        src={skillsChart}
                        // width={150}
                        // height={250}
                        alt="DCC"
                      />
                    </CardContent>
                  </Card>
                </div>
                <div className="mt-1 mr-3">
                  <Card className="rounded-lg">
                    <CardHeader className="text-lg text-gray-700">
                      <CardTitle>{userRole?.role_name}</CardTitle>
                    </CardHeader>
                    <CardContent className="pb-0">
                      <Table className="h-18">
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-[100px]">
                              Behavioural Level:
                            </TableHead>
                            <TableHead>
                              <Badge className="bg-dccblue">{userBLevel}</Badge>
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                      </Table>
                      <div className="text-sm font-semibold pt-6">
                        {" "}
                        Role description:{" "}
                      </div>
                      <div className="text-sm pt-2">
                        <p className="pb-7">{userRoleDesc}</p>
                      </div>
                      <div className="pt-3 pl-0">
                        <Button className="mr-2 ml-1 bg-[#5c2071] hover:bg-[#5c2071]">
                          Download job description
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
              {showTSkills && (
                <Card className="rounded-lg m-3 p-3">
                  <CardHeader>
                    <CardTitle className="text-lg text-gray-700">
                      Technical Skills
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <TechSkillSummary
                      filteredTechSkills={filteredTechSkills}
                      userRole={userRole && userRole.id}
                    />
                  </CardContent>
                </Card>
              )}
            </div>
            {/* END - Snapshot component */}
          </TabsContent>

          <TabsContent value="behavioural">
            <div className="flex flex-col md:flex-row mt-3">
              <div className="pl-3 pr-3 pt-0">
                <div>
                  <div>
                    <Card className="min-w-8 bg-dccpink text-white rounded-tl-xl rounded-tr-xl p-6">
                      <CardContent
                        className={
                          "font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12"
                        }
                      >
                        Think
                      </CardContent>
                    </Card>
                  </div>

                  <Accordion type="single" collapsible>
                    {behavioural_skills.map((skill, index) => {
                      if (skill.group === "Think") {
                        return (
                          <AccordionItem
                            key={skill.id}
                            value={`item-${skill.id}`}
                          >
                            <AccordionTrigger
                              className={
                                behavioural_skills.length - 1 == index + 1
                                  ? "bg-dccpink text-white font-bold p-3 hover:bg-dccpink/85 rounded-b rounded-t-none min-w-78"
                                  : "bg-dccpink text-white font-bold p-3 hover:bg-dccpink/85 rounded-b-none rounded-t-none min-w-78"
                              }
                            >
                              {skill.skill_name}
                            </AccordionTrigger>
                            <AccordionContent>
                              <div className="relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6">
                                <Carousel
                                  setApi={setCarouselApi}
                                  opts={{ loop: true }}
                                  className="mt-6 mb-3  ml-16 mr-12 max-w-xs"
                                >
                                  <CarouselContent>
                                    {skill.behavioural_sub_skills.map(
                                      (sub_skill) => (
                                        <CarouselItem key={sub_skill.id}>
                                          <Card className=" rounded-lg  mt-3">
                                            <CardHeader>
                                              <CardTitle>
                                                {sub_skill.sub_skill_name}
                                              </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                              <p className="p-0">
                                                {sub_skill[userBLevelDisplay]}
                                              </p>
                                            </CardContent>
                                          </Card>
                                        </CarouselItem>
                                      )
                                    )}
                                  </CarouselContent>
                                  <CarouselPrevious />
                                  <CarouselNext />
                                </Carousel>

                                {/* Navigation Dots */}
                                <div className=" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20">
                                  {Array.from({ length: totalItemsThink }).map(
                                    (_, index) => (
                                      <button
                                        key={index}
                                        onClick={() => scrollToIndex(index)}
                                        className={`w-3 h-3 rounded-full ${
                                          currentIndex === index
                                            ? "bg-black"
                                            : "bg-gray-300"
                                        }`}
                                      />
                                    )
                                  )}
                                </div>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        );
                      }
                    })}
                  </Accordion>
                </div>
              </div>

              <div className="p-3 pt-0">
                <div>
                  <Card className="min-w-8 bg-dccviolet text-white rounded-tl-xl rounded-tr-xl p-6">
                    <CardContent
                      className={
                        "font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12"
                      }
                    >
                      Execute
                    </CardContent>
                  </Card>
                </div>
                <Accordion type="single" collapsible>
                  {behavioural_skills.map((skill, index) => {
                    if (skill.group === "Execute") {
                      return (
                        <AccordionItem
                          key={skill.id}
                          value={`item-${skill.id}`}
                        >
                          <AccordionTrigger
                            className={
                              behavioural_skills.length - 1 == index
                                ? "bg-dccviolet text-white font-bold p-3 hover:bg-dccviolet/85 rounded-b rounded-t-none min-w-78"
                                : "bg-dccviolet text-white font-bold p-3 hover:bg-dccviolet/85 rounded-b-none rounded-t-none min-w-78"
                            }
                          >
                            {skill.skill_name}
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6">
                              <Carousel
                                setApi={setCarouselApi}
                                opts={{ loop: true }}
                                className="mt-6 mb-3  ml-16 mr-12 max-w-xs"
                              >
                                <CarouselContent>
                                  {skill.behavioural_sub_skills.map(
                                    (sub_skill) => (
                                      <CarouselItem key={sub_skill.id}>
                                        <Card className=" rounded-lg  mt-3">
                                          <CardHeader>
                                            <CardTitle>
                                              {sub_skill.sub_skill_name}
                                            </CardTitle>
                                          </CardHeader>
                                          <CardContent>
                                            <p className="p-0">
                                              {sub_skill[userBLevelDisplay]}
                                            </p>
                                          </CardContent>
                                        </Card>
                                      </CarouselItem>
                                    )
                                  )}
                                </CarouselContent>
                                <CarouselPrevious />
                                <CarouselNext />
                              </Carousel>

                              {/* Navigation Dots */}
                              <div className=" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20">
                                {Array.from({ length: totalItemsExecute }).map(
                                  (_, index) => (
                                    <button
                                      key={index}
                                      onClick={() => scrollToIndex(index)}
                                      className={`w-3 h-3 rounded-full ${
                                        currentIndex === index
                                          ? "bg-black"
                                          : "bg-gray-300"
                                      }`}
                                    />
                                  )
                                )}
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      );
                    }
                  })}
                </Accordion>
              </div>
            </div>
            <div className="flex flex-col md:flex-row mt-3 pt-3">
              <div className="p-3 pt-0">
                <div>
                  <Card className="min-w-8 bg-dccpurple text-white rounded-tl-xl rounded-tr-xl p-6">
                    <CardContent
                      className={
                        "font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12"
                      }
                    >
                      Grow
                    </CardContent>
                  </Card>
                </div>
                <Accordion type="single" collapsible>
                  {behavioural_skills.map((skill, index) => {
                    if (skill.group === "Grow") {
                      return (
                        <AccordionItem
                          key={skill.id}
                          value={`item-${skill.id}`}
                        >
                          <AccordionTrigger
                            className={
                              behavioural_skills.length - 1 == index
                                ? "bg-dccpurple text-white font-bold p-3 hover:bg-dccpurple/85 rounded-b rounded-t-none min-w-78"
                                : "bg-dccpurple text-white font-bold p-3 hover:bg-dccpurple/85 rounded-b-none rounded-t-none min-w-78"
                            }
                          >
                            {skill.skill_name}
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6">
                              <Carousel
                                setApi={setCarouselApi}
                                opts={{ loop: true }}
                                className="mt-6 mb-3  ml-16 mr-12 max-w-xs"
                              >
                                <CarouselContent>
                                  {skill.behavioural_sub_skills.map(
                                    (sub_skill) => (
                                      <CarouselItem key={sub_skill.id}>
                                        <Card className=" rounded-lg  mt-3">
                                          <CardHeader>
                                            <CardTitle>
                                              {sub_skill.sub_skill_name}
                                            </CardTitle>
                                          </CardHeader>
                                          <CardContent>
                                            <p className="p-0">
                                              {sub_skill[userBLevelDisplay]}
                                            </p>
                                          </CardContent>
                                        </Card>
                                      </CarouselItem>
                                    )
                                  )}
                                </CarouselContent>
                                <CarouselPrevious />
                                <CarouselNext />
                              </Carousel>

                              {/* Navigation Dots */}
                              <div className=" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20">
                                {Array.from({ length: totalItemsGrow }).map(
                                  (_, index) => (
                                    <button
                                      key={index}
                                      onClick={() => scrollToIndex(index)}
                                      className={`w-3 h-3 rounded-full ${
                                        currentIndex === index
                                          ? "bg-black"
                                          : "bg-gray-300"
                                      }`}
                                    />
                                  )
                                )}
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      );
                    }
                  })}
                </Accordion>
              </div>
            </div>
          </TabsContent>

          {/* technical */}

          <TabsContent value="technical">
            <div className="flex flex-col md:flex-row mt-3">
              <div className="pl-3 pr-3 pt-0">
                <div>
                  <Card className="min-w-78 bg-dccorange text-white rounded-tl-xl rounded-tr-xl p-6">
                    <CardContent
                      className={
                        "font-semibold text-2xl text-center p-0 min-w-78 ml-12 mr-12"
                      }
                    >
                      Supported Practitioner
                    </CardContent>
                  </Card>
                </div>
                <Accordion type="single" collapsible>
                  {userRole &&
                    filteredTechSkills.map((skill, index) => {
                      // skill.roll_mapping.map((mapping) => {
                      //   if (mapping.role_id === userRole) {
                      //     console.log(mapping);
                      //   }
                      // });

                      const roleMapping = skill.role_mapping.find(
                        (mapping) => mapping.role_id === userRole.id
                      );
                      console.log(roleMapping.skill_level);

                      return (
                        <AccordionItem
                          key={skill.id}
                          value={`item-${skill.id}`}
                        >
                          <AccordionTrigger
                            className={
                              "bg-dccorange text-white font-bold p-3 hover:bg-dccorange/85 rounded-b-none rounded-t-none min-w-78"
                            }
                          >
                            {skill.skill_name}
                            
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="relative px-5 mx-auto mt-5 max-w-7xl lg:mt-6">
                              <Carousel
                                setApi={setCarouselApi}
                                opts={{ loop: true }}
                                className="mt-6 mb-3  ml-16 mr-12 max-w-xs"
                              >
                                <CarouselContent>
                                  {skill.technical_sub_skills.map(
                                    (sub_skill) => (
                                      <CarouselItem key={sub_skill.id}>
                                        <Card className=" rounded-lg  mt-3">
                                          <CardHeader>
                                            <CardTitle>
                                              {sub_skill.sub_skill_name}
                                            </CardTitle>
                                          </CardHeader>
                                          <CardContent>
                                            <p className="p-0">
                                              {sub_skill[userTLevelDisplay]}
                                            </p>
                                          </CardContent>
                                        </Card>
                                      </CarouselItem>
                                    )
                                  )}
                                </CarouselContent>
                                <CarouselPrevious />
                                <CarouselNext />
                              </Carousel>

                              {/* Navigation Dots */}
                              <div className=" bottom-0 left-0 right-0 flex justify-center space-x-2 z-20">
                                {Array.from({ length: totalItems }).map(
                                  (_, index) => (
                                    <button
                                      key={index}
                                      onClick={() => scrollToIndex(index)}
                                      className={`w-3 h-3 rounded-full ${
                                        currentIndex === index
                                          ? "bg-black"
                                          : "bg-gray-300"
                                      }`}
                                    />
                                  )
                                )}
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      );
                    })}
                </Accordion>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </SidebarInset>
      {/* <main>{children}</main> */}
    </SidebarProvider>
  );
}

export const getStaticProps = async () => {
  const bdata = await supabase
    .from("behavioural_skills")
    .select(
      "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name, level_1_description, level_2_description, level_3_description, level_4_description, level_5_description)"
    );

  const techdata = await supabase
    .from("technical_skills")
    .select("id, skill_name, technical_sub_skills (id, sub_skill_name)");

  // const bdataUser = await supabase
  //   .from("behavioural_skills")
  //   .select(
  //     "id, group, skill_name, behavioural_sub_skills (id, sub_skill_name), role_mapping (id, role_id, skill_level)"
  //   );

  const techdataUser = await supabase
    .from("technical_skills")
    .select(
      "id, skill_name, role_mapping (id, role_id, skill_level), technical_sub_skills (id, sub_skill_name)"
    );

  const responses = await Promise.all([bdata, techdata, techdataUser]);

  return {
    props: {
      behavioural_skills: responses[0].data,
      technical_skills: responses[1].data,
      tech_data_user: responses[2].data,
    },
  };
};
